<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width,initial-scale=1"/>
<title>SWOOP — Pass & Play (3 dice, choose any pair) — v5.2 (Save/Load)</title>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
<style>
:root{
  --bg:#f6f7fb; --card:#ffffff; --ink:#1f2937; --muted:#6b7280;
  --accent:#2563eb; --tile:#eef0f3; --cp:#f5e5a3; --det:#f3b0a3; --center:#e6e1d6;
  --line:#c9cfd8; --danger:#ef4444;
}
*{box-sizing:border-box}
body{margin:0;background:var(--bg);font-family:Inter,system-ui,-apple-system,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>l;color:#1f2937}
.wrap{max-width:1160px;margin:28px auto 56px;padding:0 16px;}
h1{margin:0 0 10px;font-weight:800;letter-spacing:.2px}
.badges{display:flex;gap:14px;align-items:center;float:right}
.badge{background:#fff;border:1px solid var(--line);border-radius:12px;padding:6px 12px;box-shadow:0 3px 10px rgba(0,0,0,.06);display:flex;gap:8px;align-items:center}
.controls{display:flex;gap:10px;margin:8px 0 12px;flex-wrap:wrap}
button{border:0;border-radius:8px;padding:8px 12px;background:#e5e7eb;color:#111827;font-weight:700;cursor:pointer}
button.primary{background:var(--accent);color:#fff}
button.ghost{background:#f3f4f6}
button:disabled{opacity:.45;cursor:not-allowed}
.board{background:#fff;border:1px solid var(--line);border-radius:14px;padding:16px;box-shadow:0 8px 20px rgba(0,0,0,.08)}
.grid{display:grid;grid-template-columns:repeat(27,28px);grid-auto-rows:28px;gap:4px;justify-content:center;padding:10px}
.cell{width:28px;height:28px;border-radius:6px;position:relative}
.tile{background:var(--tile);border:1px solid #d7dbe1}
.cp{background:var(--cp)!important}
.det{background:var(--det)!important}
.center{background:var(--center)!important;border-style:dashed}
.highlight{outline:3px dashed #94a3b8;outline-offset:1px;cursor:pointer}
.piece{position:absolute;inset:0;display:grid;place-items:center;font-size:20px}
.piece.active{font-size:22px}
.piece.carry::after{content:'🧺'; position:absolute; right:-6px; top:-10px; font-size:14px;}
.ring{position:absolute;inset:-2px;border-radius:8px;border:2px solid var(--accent);pointer-events:none}
/* Merged final step styles */
.center.merged-final{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:2px}
.piece.basket{position:absolute;top:2px;right:2px;font-size:14px;z-index:10}
.piece.left-piece{position:relative;margin-right:4px}
.piece.right-piece{position:relative;margin-left:4px}
.center.merged-final .piece{display:inline-block;margin:0 1px}
.dir{position:absolute; bottom:-10px; left:2px; font-size:12px; color:#374151}
.dice{display:flex;gap:8px;align-items:center}
.die{width:34px;height:34px;border-radius:8px;background:#111827;color:#fff;display:grid;place-items:center;font-weight:800}
.pairs{display:flex;gap:8px;flex-wrap:wrap;margin-top:8px}
.pair{border:1px dashed var(--line);border-radius:10px;padding:6px 10px;background:#fff;cursor:pointer}
.pair.selected{border-color:var(--accent);box-shadow:0 0 0 2px rgba(37,99,235,.15)}
.note{color:var(--muted);font-size:14px;margin-top:10px}
.strip{display:flex;gap:8px;align-items:center;margin:4px 0 6px}
.toast{position:fixed;right:16px;bottom:16px;background:#111827;color:#fff;padding:10px 12px;border-radius:10px;opacity:.95}
.cell small{position:absolute;bottom:1px;right:3px;font-size:9px;color:#6b7280}
.slope{position:absolute;bottom:1px;left:3px;font-size:11px;color:#374151}
.sumLabel{position:relative;top:4px;width:28px;text-align:center;color:#6b7280;font-weight:700}
.warn{color:var(--danger); font-weight:700}
/* Modal */
.modal{position:fixed;inset:0;background:rgba(0,0,0,.45);display:none;align-items:center;justify-content:center;padding:20px}
.modal .card{background:#fff;border-radius:12px;box-shadow:0 12px 30px rgba(0,0,0,.25);max-width:760px;width:100%;padding:16px;border:1px solid var(--line)}
.modal .card h3{margin:0 0 8px}
.modal textarea{width:100%;min-height:200px;border:1px solid var(--line);border-radius:8px;padding:10px;font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace}
.modal .row{display:flex;gap:8px;justify-content:flex-end;margin-top:8px}
.small{font-size:12px;color:#6b7280}
</style>
</head>
<body>
<div class="wrap">
  <div class="strip">
    <h1>Swoop — Pass & Play</h1>
    <div class="badges">
      <div class="badge">🐒 Monkeys&nbsp;<span id="score0">0</span> • ✈️ <span id="tokens0">0</span></div>
      <div class="badge">🕊️ Seagulls&nbsp;<span id="score1">0</span> • ✈️ <span id="tokens1">1</span></div>
    </div>
  </div>

  <div id="status" style="margin:6px 0 2px;font-size:18px;font-weight:700">Loading…</div>

  <div class="controls">
    <button id="rollBtn" class="primary">Roll 3 Dice</button>
    <button id="useMoveBtn" disabled>Use Pair → Move</button>
    <button id="useSwoopBtn" disabled>Use Swoop Token</button>
    <button id="transferBtn" disabled>🔄 Transfer</button>
    <button id="cancelTransferBtn" disabled style="display:none">❌ Cancel Transfer</button>
    <button id="bankBtn" disabled>Bank (Stop)</button>
    <span style="flex:1"></span>
    <button id="newBtn" class="ghost">New Game</button>
    <button id="saveBtn" class="ghost">Save</button>
    <button id="loadBtn" class="ghost">Load</button>
    <button id="qsaveBtn" class="ghost">Quick Save</button>
    <button id="qloadBtn" class="ghost">Quick Load</button>
  </div>

  <div class="dice" id="diceRow"></div>
  <div class="pairs" id="pairRow"></div>

  <div class="board">
    <div class="grid" id="grid"></div>
    <div class="note">
      <strong>Legend:</strong> 🧺 Basket (even sums only); 🟡 Checkpoint; 🟥 Deterrent; 🐒/🐵 Monkeys; 🕊️/🦅 Seagulls.<br/>
      Steps count from each shore toward the center. Each roll is 3d6: select any <em>pair</em> of dice. Spend that pair to <strong>Move</strong> (+1 on the lane matching the sum; returning pieces move −1) or to <strong>Swoop</strong> (adjacent lane; at odd-lane top apply ↑/↓).<br/>
      <em>Bank</em> is only allowed before rolling. If a roll leaves no legal pair-sum moves <em>or</em> Swoops, press <strong>End Turn (Busted)</strong>.<br/>
      Deterrents trigger only on Bank/Bust; checkpoints are safe. <span class="warn">Cells cannot hold more than one piece.</span> Max pieces per player: <strong>5</strong>. Max <em>active pieces this turn</em>: <strong>2</strong>.
    </div>
  </div>
</div>

<div id="toast" class="toast" style="display:none"></div>

<!-- Load modal -->
<div id="loadModal" class="modal" aria-hidden="true">
  <div class="card">
    <h3>Load Game State</h3>
    <p class="small">Paste a previously saved JSON state below, or choose a file. Loading replaces the current game.</p>
    <textarea id="loadTextarea" placeholder='{"version":"v5.2","players":[...],...}'></textarea>
    <div class="row">
      <input type="file" id="fileInput" accept="application/json"/>
      <span style="flex:1"></span>
      <button id="cancelLoad">Cancel</button>
      <button id="confirmLoad" class="primary">Load</button>
    </div>
  </div>
</div>

<script>
/* ---------- Core board config ---------- */
const lanes = [
  {sum:2, L:3, basket:true},
  {sum:3, L:4, basket:false},
  {sum:4, L:5, basket:true},
  {sum:5, L:6, basket:false},
  {sum:6, L:7, basket:true},
  {sum:7, L:8, basket:false},
  {sum:8, L:7, basket:true},
  {sum:9, L:6, basket:false},
  {sum:10, L:5, basket:true},
  {sum:11, L:4, basket:false},
  {sum:12, L:3, basket:true},
];
function checkpoints(L){ const out=[2]; if(L>=6) out.push(4); out.push(L-1); out.push(L); return [...new Set(out)].filter(x=>x>=1&&x<=L); }
function deterrents(L,sum){ if(L<=3) return []; const det=[3, L-2]; if((sum===6||sum===8)&&L>=5) det.push(5); const cps=checkpoints(L); return [...new Set(det)].filter(x=>x>=1&&x<=L && !cps.includes(x)); }
const oddSlope = {3:+1, 5:-1, 7:-1, 9:-1, 11:+1};

const grid = document.getElementById('grid');
const rows = lanes.length;
const cols = 27;
// --- Geometric Board Layout (documentation)
// We model the board at two levels:
// 1) Geometry "spaces": a fixed 11-slot vertical grid per lane (1..11) that may include gaps.
//    This is used for visual alignment and for space-matching during cross‑lane Swoops.
// 2) Movement "steps": only real tiles (Normal/Checkpoint/Deterrent/Start/Final). Pieces occupy steps.
//
// TILE_MAP encodes, for each route (2..12), the type of tile at each geometric space (1..11).
// Mapping helpers:
//  - mapStepToGrid(r, step)  → space(1..11) for the lane‑local movement step (1..L[r])
//  - tileTypeAt(r, step)     → tile type at that mapped space
//  - tileExistsAt(r, step)   → true if not Gap (i.e., a usable movement tile)
//  - stepForSpace(r, space)  → best movement step on lane r for a given geometric space (exact if possible;
//                               otherwise nearest lower/upper valid step), used for Swoop lane changes.
//
// Push and gaps:
//  - If a pushed piece would land on a Gap space (within lane bounds), it snaps down (toward home) to the
//    nearest lower movement step that exists; if none, it is removed. Basket transfers to the pusher.
//  - This applies to every link of a push chain and to pushes caused by Swoops as well.
// -----------------------------------------------------------------------------
// Board geometry (grid 11 steps per lane)
const MAX_STEP = 11;
const TILE_MAP = [
  ['Start','Gap','Gap','Gap','Gap','Checkpoint','Gap','Gap','Gap','Gap','Final'],
  ['Start','Gap','Gap','Checkpoint','Gap','Gap','Gap','Checkpoint','Gap','Gap','Final'],
  ['Start','Gap','Gap','Checkpoint','Gap','Deterrent','Gap','Gap','Checkpoint','Gap','Final'],
  ['Start','Gap','Checkpoint','Gap','Deterrent','Gap','Checkpoint','Gap','Checkpoint','Gap','Final'],
  ['Start','Gap','Checkpoint','Deterrent','Gap','Checkpoint','Gap','Deterrent','Checkpoint','Gap','Final'],
  ['Start','Checkpoint','Gap','Deterrent','Checkpoint','Gap','Normal','Deterrent','Gap','Checkpoint','Final'],
  ['Start','Gap','Checkpoint','Deterrent','Gap','Checkpoint','Gap','Deterrent','Checkpoint','Gap','Final'],
  ['Start','Gap','Checkpoint','Gap','Deterrent','Gap','Checkpoint','Gap','Checkpoint','Gap','Final'],
  ['Start','Gap','Gap','Checkpoint','Gap','Deterrent','Gap','Gap','Checkpoint','Gap','Final'],
  ['Start','Gap','Gap','Checkpoint','Gap','Gap','Gap','Checkpoint','Gap','Gap','Final'],
  ['Start','Gap','Gap','Gap','Gap','Checkpoint','Gap','Gap','Gap','Gap','Final']
];
function mapStepToGrid(r, step){
  const L = lanes[r].L;
  if(L<=1) return 1;
  return 1 + Math.round((step-1)*(MAX_STEP-1)/(L-1));
}
function tileTypeAt(r, step){
  const gs = Math.max(1, Math.min(MAX_STEP, mapStepToGrid(r, step)));
  return TILE_MAP[r][gs-1] || 'Gap';
}
function tileExistsAt(r, step){ return tileTypeAt(r, step) !== 'Gap'; }

function stepForSpace(r, space) {
  // Find the best movement step for a given geometric space
  // First try to find an exact match
  const L = lanes[r].L;
  for (let step = 1; step <= L; step++) {
    if (mapStepToGrid(r, step) === space && tileExistsAt(r, step)) {
      return step;
    }
  }

  // If no exact match, find the nearest valid step
  let bestStep = null;
  let minDistance = Infinity;

  for (let step = 1; step <= L; step++) {
    if (tileExistsAt(r, step)) {
      const stepSpace = mapStepToGrid(r, step);
      const distance = Math.abs(stepSpace - space);
      if (distance < minDistance) {
        minDistance = distance;
        bestStep = step;
      }
    }
  }

  return bestStep;
}
const centerCol = 13;
const leftStartCol = 1;
const rightEndCol = cols-2;
const leftSpan = centerCol - leftStartCol - 1;
const rightSpan = rightEndCol - centerCol - 1;

const cells = [];
for(let r=0;r<rows;r++){
  const row=[];
  const labelL = document.createElement('div'); labelL.className='cell'; labelL.innerHTML=`<div class="sumLabel">${lanes[r].sum}</div>`; grid.appendChild(labelL); row.push(labelL);
  for(let c=1;c<cols-1;c++){ const d=document.createElement('div'); d.className='cell'; grid.appendChild(d); row.push(d); }
  const labelR = document.createElement('div'); labelR.className='cell'; labelR.innerHTML=`<div class="sumLabel">${lanes[r].sum}</div>`; grid.appendChild(labelR); row.push(labelR);
  cells.push(row);
}
function colForStepShared(step, L){
  // Shared-lane view: all steps 1..L-1 map to left arc, L maps to center
  if(step === L) return centerCol;
  const rel=Math.round((leftSpan-1)*(step-1)/(L-1));
  return leftStartCol+rel;
}
for(let r=0;r<rows;r++){
  const L=lanes[r].L, sum=lanes[r].sum;
  const cps=checkpoints(L), dets=deterrents(L,sum);

  // Set up steps 1..L-1 on shared (left) side
  for(let k=1;k<L;k++){
    const c=colForStepShared(k,L);
    const el=cells[r][c];
    if(tileExistsAt(r,k)){
      el.classList.add('tile'); el.dataset.r=r; el.dataset.step=k;
      const tt = tileTypeAt(r,k);
      if(tt==='Checkpoint') el.classList.add('cp');
      if(tt==='Deterrent') el.classList.add('det');
    }
    const sl=document.createElement('small'); sl.textContent=k; el.appendChild(sl);
  }

  // Shared final step at center column
  const center=cells[r][centerCol];
  center.classList.add('center');
  center.dataset.r=r; center.dataset.step=L;
  if(tileExistsAt(r,L)){
    center.classList.add('tile');
    const ttF = tileTypeAt(r,L);
    if(ttF==='Checkpoint') center.classList.add('cp');
    if(ttF==='Deterrent') center.classList.add('det');
  }
  const stepNum=document.createElement('small'); stepNum.textContent=L; center.appendChild(stepNum);
  if(lanes[r].basket){ const b=document.createElement('div'); b.className='piece basket'; b.textContent='🧺'; center.appendChild(b); }
}

/* ---------- Helpers & state ---------- */
function occupied(r, step){
  // Shared-lane occupancy: at most 1 piece per cell across both players
  for(let pi=0; pi<players.length; pi++){
    for(const pc of players[pi].pieces){
      if(pc.r===r && pc.step===step) return true;
    }
  }
  return false;
}
function pieceAt(r, step){
  for(let pi=0; pi<players.length; pi++){
    const pc = players[pi].pieces.find(p=>p.r===r && p.step===step);
    if(pc) return {pi, pc};
  }
  return null;
}
function pieceOnLane(pl, r){ return pl.pieces.find(p=>p.r===r); }
function activeCount(pl){ return pl.pieces.filter(p=>p.active===true).length; }
function isActiveForCurrent(pl, pc){ return pc.active===true; }

const players=[
  {name:'Monkeys', pieceIcon:'🐒', activeIcon:'🐵', score:0, swoopTokens:0, pieces:[]},
  {name:'Seagulls', pieceIcon:'🕊️', activeIcon:'🦅', score:0, swoopTokens:1, pieces:[]}
];
// piece: {r, step, carrying:boolean, active:boolean}

let current=0;
let rolled=null;
let selectedPair=null;
let mode='preroll';
let topStepPiece=null;
let topStepTargets=null;
let tailwindPiece=null;
let tailwindOptions=null;
let tailwindSwoopTargets=null;
let pieceChoices=null;
let selectedSum=null;
let transferSource=null;
let transferTargets=null;
let previousMode=null;

const statusEl=document.getElementById('status');
const rollBtn=document.getElementById('rollBtn');
const useMoveBtn=document.getElementById('useMoveBtn');
const useSwoopBtn=document.getElementById('useSwoopBtn');
const bankBtn=document.getElementById('bankBtn');
const newBtn=document.getElementById('newBtn');
const saveBtn=document.getElementById('saveBtn');
const loadBtn=document.getElementById('loadBtn');
const qsaveBtn=document.getElementById('qsaveBtn');
const qloadBtn=document.getElementById('qloadBtn');
const diceRow=document.getElementById('diceRow');
const pairRow=document.getElementById('pairRow');
const toastEl=document.getElementById('toast');

/* ---------- UI helpers ---------- */
function toast(m){ toastEl.textContent=m; toastEl.style.display='block'; setTimeout(()=>toastEl.style.display='none',1500); }

function locate(r, step){
  if(step===999) return cells[r][centerCol];
  const L=lanes[r].L; if(step<1 || step> L) return null;
  if(step === L) return cells[r][centerCol];
  const c=colForStepShared(step,L); return cells[r][c];
}

function clearPieces(){
  for(const row of cells) for(const el of row){
    const old=el.querySelectorAll('.piece,.ring,.slope,.dir');
    old.forEach(n=>n.remove());
    el.classList.remove('highlight'); el.onclick=null;
  }
  // Center baskets (visual only)
  for(let r=0;r<rows;r++){
    const ce=cells[r][centerCol];
    if(lanes[r].basket && !ce.querySelector('.piece')){
      const b=document.createElement('div'); b.className='piece'; b.textContent='🧺'; ce.appendChild(b);
    }
  }
}
function placePieces(){
  for(let pi=0; pi<players.length; pi++){
    const pl=players[pi];
    for(const pc of pl.pieces){
      const el=locate(pc.r, pc.step); if(!el) continue;
      const div=document.createElement('div'); 
      const useActiveIcon = (pi===current && isActiveForCurrent(pl,pc));
      div.className='piece'+(useActiveIcon?' active':'');
      div.textContent = useActiveIcon ? pl.activeIcon : pl.pieceIcon;
      if(pc.carrying){ div.classList.add('carry'); const d=document.createElement('div'); d.className='dir'; d.textContent='↩'; el.appendChild(d); }
      el.appendChild(div);
      if(useActiveIcon){ const ring=document.createElement('div'); ring.className='ring'; el.appendChild(ring); }
    }
  }
}
function renderScores(){
  document.getElementById('score0').textContent=players[0].score;
  document.getElementById('score1').textContent=players[1].score;
  const t0=document.getElementById('tokens0'); if(t0) t0.textContent=players[0].swoopTokens||0;
  const t1=document.getElementById('tokens1'); if(t1) t1.textContent=players[1].swoopTokens||0;
}

function checkVictory(){
  const TARGET_SCORE = 2;
  for(let i = 0; i < players.length; i++){
    if(players[i].score >= TARGET_SCORE){
      mode = 'gameOver';
      statusEl.textContent = `🎉 ${players[i].name} wins with ${players[i].score} deliveries!`;
      rollBtn.disabled = true;
      useMoveBtn.disabled = true;
      useSwoopBtn.disabled = true;
      bankBtn.disabled = true;
      return true;
    }
  }
  return false;
}

function tryPickupBasket(pc){
  const L=lanes[pc.r].L;
  if(pc.step===L && lanes[pc.r].basket && !pc.carrying){
    const ce=cells[pc.r][centerCol];
    const basket=ce.querySelector('.piece');
    if(basket){
      basket.remove();
      pc.carrying=true;
      toast('Picked up basket!');
      return true;
    }
  }
  return false;
}

/* ---------- Buttons enable/disable ---------- */
function canSwoopNow(){
  const pl=players[current];
  if(!(pl.swoopTokens>0)) return false;
  for(const pc of pl.pieces){ if(pc.active && potentialSwoops(pc).length>0) return true; }
  return false;
}
function anyMandatoryActionThisRoll(){ return existsAnyMoveThisRoll(); }
function anyActionThisRoll(){ return existsAnyMoveThisRoll() || canSwoopNow(); }

function render(){
  clearPieces(); placePieces(); renderScores();

  // Handle game over mode
  if(mode === 'gameOver') {
    rollBtn.disabled = true;
    useMoveBtn.disabled = true;
    useSwoopBtn.disabled = true;
    transferBtn.disabled = true;
    bankBtn.disabled = true;
    return;
  }

  rollBtn.disabled = !(mode==='preroll');
  const canMove = (mode==='pairChosen' && selectedPair && canMoveOnSum(players[current], selectedPair.sum));
  useMoveBtn.disabled = !canMove;
  const canSwoop = canSwoopNow();
  useSwoopBtn.disabled = !canSwoop;
  transferBtn.disabled = !canTransfer();

  // Show/hide cancel transfer button
  if(mode === 'chooseTransferSource' || mode === 'chooseTransferTarget'){
    cancelTransferBtn.style.display = 'inline-block';
    cancelTransferBtn.disabled = false;
  } else {
    cancelTransferBtn.style.display = 'none';
    cancelTransferBtn.disabled = true;
  }

  if(mode==='rolled' || mode==='pairChosen'){
    const mandatory = anyMandatoryActionThisRoll();
    const any = anyActionThisRoll();
    if(mandatory) {
      bankBtn.textContent = 'Must Move';
      bankBtn.disabled = true;
    } else if(any) {
      bankBtn.textContent = 'Bank (Stop)';
      bankBtn.disabled = false;
    } else {
      bankBtn.textContent = 'End Turn (Busted)';
      bankBtn.disabled = false;
    }
  }else{ bankBtn.textContent='Bank (Stop)'; bankBtn.disabled=!(mode==='preroll'); }
}

/* ---------- Start/Next ---------- */
function startGame(){ 
  players[0].pieces=[]; players[1].pieces=[]; players[0].score=0; players[1].score=0;
  current=0; rolled=null; selectedPair=null; mode='preroll';
  diceRow.innerHTML=''; pairRow.innerHTML='';
  render(); statusEl.textContent=`${players[current].name}, roll the dice!`;
}
function nextPlayer(){ 
  players[current].pieces.forEach(p=>p.active=false);
  current=1-current; rolled=null; selectedPair=null; mode='preroll'; diceRow.innerHTML=''; pairRow.innerHTML='';
  render(); statusEl.textContent=`${players[current].name}, roll the dice!`; 
}

/* ---------- Dice / Pairs ---------- */
function r6(){ return 1+Math.floor(Math.random()*6); }
function renderDiceAndPairs(){
  if(!rolled){ diceRow.innerHTML=''; pairRow.innerHTML=''; return; }
  const d=rolled.d;
  diceRow.innerHTML=d.map(v=>`<div class="die">${v}</div>`).join('');
  pairRow.innerHTML=rolled.pairs.map((p,i)=>`<div class="pair ${selectedPair && selectedPair.i===p.i && selectedPair.j===p.j?'selected':''}" data-i="${i}">${d[p.i]} + ${d[p.j]} = <b>${p.sum}</b></div>`).join('');
  Array.from(pairRow.children).forEach(el=>{
    el.onclick=()=>{
      // Allow pair selection from rolled, pairChosen, or swoop modes (to deselect swoop)
      if(mode !== 'rolled' && mode !== 'pairChosen' && mode !== 'chooseSwoop' && mode !== 'pickSwoopDest') return;

      Array.from(pairRow.children).forEach(x=>x.classList.remove('selected'));
      el.classList.add('selected');
      selectedPair=rolled.pairs[+el.dataset.i];
      mode='pairChosen';

      // Clear any swoop-related highlights when switching pairs
      clearHighlights();

      render();
      updateStatusAfterSelect();
    };
  });
}
function roll3(){
  const d=[r6(),r6(),r6()]; rolled={ d, pairs: [[0,1],[0,2],[1,2]].map(([i,j])=>({i,j,sum:d[i]+d[j]})) };
  selectedPair=null; mode='rolled'; renderDiceAndPairs();
  if(!existsAnyMoveThisRoll()){
    render();
    statusEl.textContent=`${players[current].name} rolled ${d.join(' ')} — select a pair to Move, or spend a Swoop token, or End Turn (Busted).`;
  } else {
    render();
    statusEl.textContent=`${players[current].name}: select a pair to Move, or spend a Swoop token.`;
  }
}
function updateStatusAfterSelect(){
  if(mode!=='pairChosen') return;
  const canMove = canMoveOnSum(players[current], selectedPair.sum);
  const canSwoop = canSwoopNow();
  if(canMove && canSwoop) statusEl.textContent=`${players[current].name}: Move or spend a Swoop token.`;
  else if(canMove) statusEl.textContent=`${players[current].name}: Move.`;
  else if(canSwoop) statusEl.textContent=`${players[current].name}: Spend a Swoop token (optional) or End Turn (Busted).`;
  else statusEl.textContent=`${players[current].name}: End Turn (Busted).`;
}

/* ---------- Move/Swoop rules ---------- */
function existsAnyMoveThisRoll(){
  if(!rolled) return false;
  const pl=players[current];
  for(const pr of rolled.pairs){ if(canMoveOnSum(pl, pr.sum)) return true; }
  return false;
}
function canMoveOnSum(pl, sum){
  const r=lanes.findIndex(x=>x.sum===sum); if(r<0) return false;

  // Get all pieces on this route
  const piecesOnRoute = pl.pieces.filter(p => p.r === r);

  if(piecesOnRoute.length > 0){
    // Check if any piece on this route can move
    // First check active pieces, then inactive pieces
    const activePieces = piecesOnRoute.filter(p => p.active);
    const inactivePieces = piecesOnRoute.filter(p => !p.active);

    // Check active pieces first - they can move if not blocked
    for(const pc of activePieces){
      const L=lanes[pc.r].L;
      if(pc.step === L){
        const targets = getMoveTargets(pc);
        if(targets.length > 0) return true;
        if(canTopStepActivate(pl, pc)) return true;
      } else {
        const targets = getMoveTargets(pc);
        if(targets.length > 0) return true;
      }
    }

    // Check inactive pieces - they can move if they can be activated first
    if(activeCount(pl) < 2){
      for(const pc of inactivePieces){
        const L=lanes[pc.r].L;
        if(pc.step === L){
          const targets = getMoveTargets(pc);
          if(targets.length > 0) return true;
          if(canTopStepActivate(pl, pc)) return true;
        } else {
          const targets = getMoveTargets(pc);
          if(targets.length > 0) return true;
        }
      }
    }
  
    return false;
  } else {
    // No pieces on route - check if we can spawn a new piece
    return (pl.pieces.length<5 && !occupied(r, 1) && activeCount(pl)<2);
  }
}

// Check if a piece at top step can be activated
function canTopStepActivate(pl, pc){
  return !pc.active && activeCount(pl) < 2;
}

// Check if a piece at top step can move down
function canTopStepMoveDown(pc){
  const L = lanes[pc.r].L;
  if(pc.step !== L) return false;
  const downStep = L - 1;
  return downStep >= 1;
}

// Check if a piece at top step can do a free swoop
function canTopStepFreeSwoop(pc){
  if(pc.step !== lanes[pc.r].L) return false;
  return potentialTopStepSwoops(pc).length > 0;
}

// Get potential swoop targets for a piece at top step
function potentialTopStepSwoops(pc){
  const targets = [];
  const r = pc.r;
  const L = lanes[r].L;

  if(pc.step !== L) return targets;

  for(const dr of [-1, +1]){
    const r2 = r + dr;
    if(r2 < 0 || r2 >= lanes.length) continue;

    const step2 = lanes[r2].L;
    if(tileExistsAt(r2, step2)) targets.push({r: r2, step: step2});
  }
  return targets;
}

// Get potential move destinations for a piece (up, down, and sideways if at top step)
function getMoveTargets(pc){
  const targets = [];
  const L = lanes[pc.r].L;

  // Up
  const up = pc.step + 1;
  if(up <= L && tileExistsAt(pc.r, up)){
    targets.push({r: pc.r, step: up});
  }

  // Down
  const down = pc.step - 1;
  if(down >= 1 && tileExistsAt(pc.r, down)){
    targets.push({r: pc.r, step: down});
  }

  // Sideways from top step
  if(pc.step === L){
    for(const dr of [-1, +1]){
      const r2 = pc.r + dr;
      if(r2 < 0 || r2 >= rows) continue;
      const step2 = lanes[r2].L;
      if(tileExistsAt(r2, step2)) targets.push({r: r2, step: step2});
  }
  }
  return targets;
}
function ensurePieceForSum(pl, sum){
  const r = lanes.findIndex(x=>x.sum===sum);

  // Get all pieces on this route
  const piecesOnRoute = pl.pieces.filter(p => p.r === r);

  if(piecesOnRoute.length > 0){
    // Get all viable pieces (active pieces that can move + inactive pieces that can be activated and move)
    const viablePieces = [];

    // Check active pieces that can move
    const activePieces = piecesOnRoute.filter(p => p.active);
    for(const pc of activePieces){
      const L = lanes[pc.r].L;
      if(pc.step === L){
        // Top step pieces can always be "activated" (even if already active)
        viablePieces.push(pc);
      } else {
        const targets = getMoveTargets(pc);
        if(targets.length > 0){
          viablePieces.push(pc);
        }
      }
    }

    // Check inactive pieces that can be activated (if under the 2-piece limit)
    if(activeCount(pl) < 2){
      const inactivePieces = piecesOnRoute.filter(p => !p.active);
      for(const pc of inactivePieces){
        const L = lanes[pc.r].L;
        if(pc.step === L){
          // Top step pieces can always be activated
          viablePieces.push(pc);
        } else {
          const targets = getMoveTargets(pc);
          if(targets.length > 0){
            viablePieces.push(pc);
          }
        }
      }
    }

    // If multiple viable pieces, let player choose
    if(viablePieces.length > 1){
      return 'CHOOSE_PIECE'; // Special return value to trigger piece selection
    } else if(viablePieces.length === 1){
      const pc = viablePieces[0];
      const L = lanes[pc.r].L;

      if(pc.step === L){
        return ensureTopStepPiece(pl, pc);
      }

      // Activate if not already active
      if(!pc.active && activeCount(pl) < 2){
        pc.active = true;
      }
      return pc;
    }

    // No viable pieces
    return null;
  }

  // No pieces on route - try to spawn a new piece
  if(pl.pieces.length>=5 || activeCount(pl)>=2) return null;
  if(occupied(r, 1)) return null;
  const pc = {r, step:1, carrying:false, active:true};
  pl.pieces.push(pc);
  return pc;
}

// Get all viable pieces for a sum (used for piece selection UI)
function getViablePiecesForSum(pl, sum){
  const r = lanes.findIndex(x=>x.sum===sum);
  if(r < 0) return [];

  const piecesOnRoute = pl.pieces.filter(p => p.r === r);
  const viablePieces = [];

  // Check active pieces that can move
  const activePieces = piecesOnRoute.filter(p => p.active);
  for(const pc of activePieces){
    const L = lanes[pc.r].L;
    if(pc.step === L){
      // Top step pieces can always be "activated" (even if already active)
      viablePieces.push(pc);
    } else {
      const targets = getMoveTargets(pc);
      if(targets.length > 0){
        viablePieces.push(pc);
      }
    }
  }

  // Check inactive pieces that can be activated (if under the 2-piece limit)
  if(activeCount(pl) < 2){
    const inactivePieces = piecesOnRoute.filter(p => !p.active);
    for(const pc of inactivePieces){
      const L = lanes[pc.r].L;
      if(pc.step === L){
        // Top step pieces can always be activated
        viablePieces.push(pc);
      } else {
        const targets = getMoveTargets(pc);
        if(targets.length > 0){
          viablePieces.push(pc);
        }
      }
    }
  }

  return viablePieces;
}

// Handle pieces at top step with multiple options
function ensureTopStepPiece(pl, pc){
  // First, try to activate if not already active
  if(!pc.active && activeCount(pl) < 2){
    pc.active = true;
  }
  return pc;
}

// Choose the best action for a piece at top step
function chooseTopStepAction(pc){
  // Prefer move down if carrying (helps get home faster)
  if(pc.carrying && canTopStepMoveDown(pc)){
    return 'move_down';
  }

  // Otherwise prefer free swoop if available
  if(canTopStepFreeSwoop(pc)){
    return 'free_swoop';
  }

  // Default to just activation (no movement)
  return 'activate';
}

// Choose the best target for a top step free swoop
function chooseBestTopStepSwoopTarget(targets, pc){
  if(targets.length === 0) return null;

  // If carrying, prefer lanes that help get home (even sums with baskets)
  if(pc.carrying){
    const basketTargets = targets.filter(t => lanes[t.r].basket);
    if(basketTargets.length > 0){
      return basketTargets[0];
    }
  }

  // Otherwise, prefer higher sum lanes (better positioning)
  targets.sort((a, b) => lanes[b.r].sum - lanes[a.r].sum);
  return targets[0];
}
function afterMovePickup(pc){
  const lane=lanes[pc.r]; const L=lane.L;
  if(lane.basket && pc.step===L && !pc.carrying){
    const ce=cells[pc.r][centerCol]; const basket=ce.querySelector('.piece');
    if(basket){ basket.remove(); pc.carrying=true; toast('Picked up basket!'); }
  }
}

// Return a basket to the lane's top step (if not already present)
function returnBasketToTop(r){
  const lane = lanes[r];
  if(!lane.basket) return; // only even lanes can host baskets
  const ce=cells[r][centerCol];
  if(!ce.querySelector('.piece')){
    const b=document.createElement('div'); b.className='piece'; b.textContent='🧺'; ce.appendChild(b);
  }
}

// Apply push chain from an approaching vector
// origin: {r, step}, dest: {r, step}
// Push rules summary:
//  - Compute push vector from mover: (dr, ds) across lanes/steps.
//  - If target tile is occupied, recursively push the occupant along (dr, ds).
//  - Snap‑down: if the computed landing space is within the lane but maps to a Gap, drop to the nearest
//    lower real step; remove if none. Apply at each link in the chain.
//  - Basket transfer: if the pushed piece is carrying and the immediate pusher is not, transfer the basket
//    to the pusher before moving it.
function nearestValidStepDown(r, step){
  let s = step;
  while(s >= 1 && !tileExistsAt(r, s)) s--;
  return s;
}
function applyPushChain(origin, dest, pusher, isSwoop = false){
  const dr = dest.r - origin.r;
  const ds = isSwoop ? 0 : (dest.step - origin.step); // For swoops, don't push in step direction
  if(dr===0 && ds===0) return;

  // attempt to push occupant at dest, if any
  const occ = pieceAt(dest.r, dest.step);
  if(!occ) return; // no one to push

  const r2 = dest.r + dr;
  const L2 = (r2>=0 && r2<lanes.length) ? lanes[r2].L : -1;
  let s2 = dest.step + ds;

  // Basket transfer to pusher (if the pushed piece was carrying and pusher can take it)
  if(occ.pc.carrying && pusher && !pusher.carrying){
    pusher.carrying = true;
    occ.pc.carrying = false;
  }

  // if out of bounds, remove the piece
  if(r2 < 0 || r2 >= lanes.length){
    // remove occ.pc from players[occ.pi].pieces
    const owner = players[occ.pi];
    const idx = owner.pieces.indexOf(occ.pc);
    if(idx>=0) owner.pieces.splice(idx,1);
    return;
  }

  // clamp and snap down to nearest valid tile
  s2 = Math.max(1, Math.min(L2, s2));
  s2 = nearestValidStepDown(r2, s2);
  if(s2 < 1){
    const owner = players[occ.pi];
    const idx = owner.pieces.indexOf(occ.pc);
    if(idx>=0) owner.pieces.splice(idx,1);
    return;
  }

  // recurse to push next occupant, with current occupant as pusher
  applyPushChain(dest, {r:r2, step:s2}, occ.pc, isSwoop);

  // after clearing next spot, move this occupant forward
  occ.pc.r = r2;
  occ.pc.step = s2;
}

// Perform a move or swoop with push resolution
function performMoveWithPush(pc, target, isSwoop = false){
  const origin = {r: pc.r, step: pc.step};
  // resolve chain pushes at target
  applyPushChain(origin, target, pc, isSwoop);
  // move the moving piece
  pc.r = target.r;
  pc.step = target.step;
  // then pickup basket if applicable
  tryPickupBasket(pc);
}

// Transfer functionality
function canTransfer(){
  // Allow transfers during any mode of the current player's turn (except game over and opponent's tailwind)
  if(mode === 'gameOver') return false;
  if(mode === 'tailwind') return false; // Opponent's turn

  const pl = players[current];
  return pl.pieces.some(pc => pc.carrying);
}

function getTransferTargets(sourcePiece){
  const pl = players[current];
  const targets = [];

  for(const pc of pl.pieces){
    if(pc === sourcePiece || pc.carrying) continue; // Can't transfer to self or carrying pieces

    const sameLane = pc.r === sourcePiece.r;
    const sameStep = pc.step === sourcePiece.step;
    const stepDiff = Math.abs(pc.step - sourcePiece.step);
    const laneDiff = Math.abs(pc.r - sourcePiece.r);

    // Adjacent on same lane (step ±1)
    if(sameLane && stepDiff === 1){
      targets.push(pc);
    }
    // Adjacent on different lane (same step)
    else if(!sameLane && sameStep && laneDiff === 1){
      targets.push(pc);
    }
    // Diagonally 1 step away on different lane
    else if(!sameLane && stepDiff === 1 && laneDiff === 1){
      targets.push(pc);
    }
  }

  return targets;
}

function startTransfer(){
  if(!canTransfer()) return;
  previousMode = mode;
  mode = 'chooseTransferSource';
  statusEl.textContent = `${players[current].name}: Click a piece carrying a basket to transfer from.`;
  clearHighlights();

  // Highlight pieces carrying baskets
  const pl = players[current];
  for(const pc of pl.pieces){
    if(pc.carrying){
      const el = locate(pc.r, pc.step);
      if(el){
        el.classList.add('highlight');
        el.onclick = () => selectTransferSource(pc);
      }
    }
  }
}

function selectTransferSource(piece){
  if(!piece.carrying) return;
  const targets = getTransferTargets(piece);
  if(targets.length === 0){
    toast('No valid transfer targets for this piece.');
    return;
  }

  transferSource = piece;
  transferTargets = targets;
  mode = 'chooseTransferTarget';
  statusEl.textContent = `${players[current].name}: Click a piece to transfer the basket to.`;
  clearHighlights();

  // Highlight transfer targets
  for(const pc of targets){
    const el = locate(pc.r, pc.step);
    if(el){
      el.classList.add('highlight');
      el.onclick = () => executeTransfer(pc);
    }
  }
}

function executeTransfer(targetPiece){
  if(!transferSource || !targetPiece) return;

  transferSource.carrying = false;
  targetPiece.carrying = true;
  toast('Basket transferred!');

  // Determine what mode to return to
  const returnMode = previousMode || 'preroll';
  const pl = players[current];

  // Return to the previous mode, preserving game state
  mode = returnMode;
  transferSource = null;
  transferTargets = null;
  previousMode = null;
  clearHighlights();

  // Set appropriate message based on the mode we're returning to
  if(returnMode === 'preroll'){
    const hasMoreCarryingPieces = pl.pieces.some(pc => pc.carrying);
    if(hasMoreCarryingPieces){
      statusEl.textContent = `${pl.name}: Roll, Bank, or Transfer again.`;
    } else {
      statusEl.textContent = `${pl.name}: Roll or Bank.`;
    }
  } else if(returnMode === 'rolled'){
    statusEl.textContent = `${pl.name}: Choose a pair to move or Bank/Bust.`;
  } else if(returnMode === 'pairChosen'){
    statusEl.textContent = `${pl.name}: Move, Swoop, or Bank/Bust.`;
  } else {
    statusEl.textContent = `${pl.name}: Continue your turn.`;
  }

  render();
}

function cancelTransfer(){
  const returnMode = previousMode || 'preroll';
  const pl = players[current];

  mode = returnMode;
  transferSource = null;
  transferTargets = null;
  previousMode = null;
  clearHighlights();

  // Set appropriate message based on the mode we're returning to
  if(returnMode === 'preroll'){
    const hasCarryingPieces = pl.pieces.some(pc => pc.carrying);
    if(hasCarryingPieces){
      statusEl.textContent = `${pl.name}: Roll, Bank, or Transfer.`;
    } else {
      statusEl.textContent = `${pl.name}: Roll or Bank.`;
    }
  } else if(returnMode === 'rolled'){
    statusEl.textContent = `${pl.name}: Choose a pair to move or Bank/Bust.`;
  } else if(returnMode === 'pairChosen'){
    statusEl.textContent = `${pl.name}: Move, Swoop, or Bank/Bust.`;
  } else {
    statusEl.textContent = `${pl.name}: Continue your turn.`;
  }

  render();
}
function usePairForMove(){
  if(!(mode==='pairChosen' && selectedPair)) return;
  const pl = players[current]; const s=selectedPair.sum;
  if(!canMoveOnSum(pl, s)){ toast('That sum cannot move.'); return; }
  const beforeCount = pl.pieces.length;
  const pc = ensurePieceForSum(pl, s);

  // Check if we need to let the player choose which piece to use
  if(pc === 'CHOOSE_PIECE'){
    const viablePieces = getViablePiecesForSum(pl, s);
    mode = 'choosePiece';
    pieceChoices = viablePieces;
    selectedSum = s;
    clearHighlights();
    for(const piece of viablePieces){
      const cell = locate(piece.r, piece.step);
      if(cell){
        cell.classList.add('highlight');
        cell.onclick = () => selectPieceForMove(piece);
      }
    }
    statusEl.textContent = `${pl.name}: Choose which piece to activate/move.`;
    return;
  }

  if(!pc) { toast('No available piece/slot.'); return; }

  if(pl.pieces.length>beforeCount){
    // just activated new lane; piece remains at step 1
  }else{
    // General movement: up/down anywhere; if at top, also sideways
    const targets = getMoveTargets(pc);
    if(targets.length === 0){
      // No movement possible (maybe just activated)
    } else if(targets.length === 1){
      const target = targets[0];
      performMoveWithPush(pc, target);
    } else {
      // Multiple choices — let user select destination (up/down/sideways)
      mode = 'chooseMoveDest';
      clearHighlights();
      for(const target of targets){
        const cell = locate(target.r, target.step);
        if(cell){
          cell.classList.add('highlight');
          cell.onclick = () => {
            performMoveWithPush(pc, target);

            rolled=null; selectedPair=null; mode='preroll';
            diceRow.innerHTML=''; pairRow.innerHTML='';
            clearHighlights();
            render();
            statusEl.textContent = `${pl.name} acted on ${s}. Roll or Bank.`;
          };
        }
      }
      statusEl.textContent = `${pl.name}: Choose Up, Down, or Sideways.`;
      return;
    }
  }
  rolled=null; selectedPair=null; mode='preroll'; diceRow.innerHTML=''; pairRow.innerHTML='';
  render(); statusEl.textContent = `${pl.name} acted on ${s}. Roll or Bank.`;
}

function selectPieceForMove(selectedPiece){
  if(mode !== 'choosePiece' || !pieceChoices || !selectedSum) return;

  const pl = players[current];
  const sum = selectedSum;

  // Find the actual piece in the player's pieces array
  const pc = pl.pieces.find(p => p.r === selectedPiece.r && p.step === selectedPiece.step);

  if(!pc) return;

  const L = lanes[pc.r].L;

  // Handle top step pieces
  if(pc.step === L){
    if(!pc.active && activeCount(pl) < 2){
      pc.active = true;
    }
    // Continue with normal flow for top step pieces
  } else {
    // Activate if not already active
    if(!pc.active && activeCount(pl) < 2){
      pc.active = true;
    }
  }

  // Clear piece selection state
  mode = 'pairChosen';
  pieceChoices = null;
  selectedSum = null;
  clearHighlights();

  // Now proceed with movement logic
  const targets = getMoveTargets(pc);
  if(targets.length === 0){
    // No movement possible (maybe just activated)
    rolled=null; selectedPair=null; mode='preroll';
    diceRow.innerHTML=''; pairRow.innerHTML='';
    render();
    statusEl.textContent = `${pl.name}: Roll or Bank.`;
  } else if(targets.length === 1){
    // Auto-apply single move
    const target = targets[0];
    performMoveWithPush(pc, target);

    rolled=null; selectedPair=null; mode='preroll';
    diceRow.innerHTML=''; pairRow.innerHTML='';
    render();
    statusEl.textContent = `${pl.name}: Roll or Bank.`;
  } else {
    // Multiple choices — let user select destination (up/down/sideways)
    mode = 'chooseMoveDest';
    for(const target of targets){
      const cell = locate(target.r, target.step);
      if(cell){
        cell.classList.add('highlight');
        cell.onclick = () => {
          performMoveWithPush(pc, target);

          rolled=null; selectedPair=null; mode='preroll';
          diceRow.innerHTML=''; pairRow.innerHTML='';
          clearHighlights();
          render();
          statusEl.textContent = `${pl.name} acted on ${sum}. Roll or Bank.`;
        };
      }
    }
    statusEl.textContent = `${pl.name}: Choose Up, Down, or Sideways.`;
  }
}

function potentialSwoops(pc){
  const targets=[]; const r=pc.r; const L=lanes[r].L; const sum=lanes[r].sum;
  const atOddTop = (sum%2===1) && (pc.step===L-1);
  const atTopStep = pc.step===L;

  for(const dr of [-1,+1]){
    const r2=r+dr; if(r2<0||r2>=rows) continue;
    let step2;

    if(atTopStep){
      // Pieces at the top step can swoop to the top step of adjacent lanes
      step2 = lanes[r2].L;
    } else {
      // Use geometric space mapping for all other cases
      const space = mapStepToGrid(r, pc.step);
      step2 = stepForSpace(r2, space);
    }

    if(step2 && tileExistsAt(r2, step2)) targets.push({r:r2, step:step2});
  }
  return targets;
}
function usePairForSwoop(){
  // Use swoop token instead of pair
  const pl=players[current];
  if(pl.swoopTokens<=0){ toast('No swoop tokens.'); return; }
  const eligible = pl.pieces.filter(p=>p.active && potentialSwoops(p).length>0);
  if(eligible.length===0){ toast('No eligible piece to Swoop.'); return; }
  mode='chooseSwoop';
  clearPieces(); placePieces();
  statusEl.textContent = `${pl.name}: spend a token — click an active piece to Swoop.`;
  for(const pc of eligible){
    const el = locate(pc.r, pc.step); if(!el) continue;
    el.classList.add('highlight'); el.onclick = ()=>{
      clearHighlights();
      const dests = potentialSwoops(pc);
      for(const d of dests){
        const cell = locate(d.r, d.step);
        if(!cell) continue;
        cell.classList.add('highlight');
        if((lanes[pc.r].sum%2===1) && (pc.step===lanes[pc.r].L-1)){
          const s=document.createElement('div'); s.className='slope'; s.textContent = (oddSlope[lanes[pc.r].sum]===1?'↑':'↓'); cell.appendChild(s);
        }
        cell.onclick = ()=>{
          // spend token and swoop
          pl.swoopTokens -= 1; renderScores();
          performMoveWithPush(pc, d, true); // isSwoop = true
          // Token swoop completes the action for this roll — exit roll context
          rolled=null; selectedPair=null; mode='preroll';
          clearHighlights();
          render(); statusEl.textContent = `${pl.name}: Roll or Bank.`;
        };
      }
    };
  }
}
function clearHighlights(){ for(const row of cells) for(const el of row){ el.classList.remove('highlight'); el.onclick=null; const hints=el.querySelectorAll('.slope'); hints.forEach(n=>n.remove()); } placePieces(); }

/* ---------- Tailwind ---------- */
function triggerTailwindSelection(){
  mode='tailwind'; render();
  const oppIndex = 1-current; const opp = players[oppIndex];
  let hasOption=false;
  if(opp.pieces.length>0){
    statusEl.textContent = `${players[oppIndex].name} Tailwind: choose a piece to advance +1, or click a base to spawn.`;
    for(const pc of opp.pieces){
      const el = locate(pc.r, pc.step); if(!el) continue;
      const L=lanes[pc.r].L; const dir = pc.carrying ? -1 : +1; const ns = pc.step + dir;

      // Check if this piece can advance
      let canAdvance = false;
      if (ns > L) {
        // Non-carrying piece can advance beyond final step (gets removed)
        canAdvance = !pc.carrying;
      } else if (ns >= 1) {
        // Normal advancement within lane bounds
        canAdvance = true;
      }

      if (canAdvance) {
        el.classList.add('highlight');
        el.onclick = ()=>{
          if (ns > L && !pc.carrying) {
            // Non-carrying piece at top step - give player choice between swoop and move down
            if (pc.step === L) {
              const canMoveDown = canTopStepMoveDown(pc);
              const canSwoop = canTopStepFreeSwoop(pc);

              if (canMoveDown || canSwoop) {
                // Set up choice mode for tailwind top step options
                mode = 'tailwindTopStepChoice';
                tailwindPiece = pc;
                tailwindOptions = [];
                if (canMoveDown) tailwindOptions.push('move_down');
                if (canSwoop) tailwindOptions.push('swoop');

                clearHighlights();
                showTailwindTopStepChoice();
                return;
              } else {
                // No options available, remove piece
                const index = opp.pieces.indexOf(pc);
                if (index > -1) opp.pieces.splice(index, 1);
              }
            } else {
              // Remove piece that advances beyond final step
              const index = opp.pieces.indexOf(pc);
              if (index > -1) opp.pieces.splice(index, 1);
            }
          } else if (ns >= 1 && ns <= L) {
            // Normal advancement with push
            performMoveWithPush(pc, {r: pc.r, step: ns});
          }
          finishTailwind();
        };
        hasOption=true;
      }
    }
    if(opp.pieces.length < 5){
      for(let r=0;r<rows;r++){
        const base = locate(r, 1);
        if(base && !occupied(r, 1)){
          base.classList.add('highlight'); base.onclick = ()=>{
            opp.pieces.push({r, step:1, carrying:false, active:false});
            finishTailwind();
          }; hasOption=true;
        }
      }
    }
  } else {
    statusEl.textContent = `${players[oppIndex].name} Tailwind: click a base (step 1) to spawn a piece.`;
    for(let r=0;r<rows;r++){
      const base = locate(r, 1);
      if(base && !occupied(r, 1)){
        base.classList.add('highlight'); base.onclick = ()=>{
          opp.pieces.push({r, step:1, carrying:false, active:false});
          finishTailwind();
        }; hasOption=true;
      }
    }
  }
  if(!hasOption) finishTailwind();
  function finishTailwind(){
    clearHighlights(); mode='preroll'; render(); statusEl.textContent = `${players[current].name}: Roll or Bank.`;
  }
}

function showTailwindTopStepChoice(){
  const oppIndex = 1-current;
  statusEl.innerHTML = `
    ${players[oppIndex].name} Tailwind: Choose action for piece at top step:<br>
    ${tailwindOptions.includes('move_down') ? '<button onclick="handleTailwindTopStepChoice(\'move_down\')">⬇️ Move Down</button>' : ''}
    ${tailwindOptions.includes('swoop') ? '<button onclick="handleTailwindTopStepChoice(\'swoop\')">🔄 Swoop</button>' : ''}
  `;
}

function handleTailwindTopStepChoice(action){
  if(mode !== 'tailwindTopStepChoice' || !tailwindPiece) return;

  const oppIndex = 1-current;
  const opp = players[oppIndex];

  // Find the actual piece in the opponent's pieces array
  const actualPiece = opp.pieces.find(p => p.r === tailwindPiece.r && p.step === tailwindPiece.step);

  if (!actualPiece) {
    finishTailwind();
    return;
  }

  if (action === 'move_down') {
    const L = lanes[actualPiece.r].L;
    const downStep = L - 1;
    if (downStep >= 1) {
      performMoveWithPush(actualPiece, {r: actualPiece.r, step: downStep});
      toast(`Moved down to step ${downStep}`);
    }
  } else if (action === 'swoop') {
    const targets = potentialTopStepSwoops(actualPiece);
    if (targets.length === 1) {
      // Auto-select single target
      const target = targets[0];
      performMoveWithPush(actualPiece, target, true); // isSwoop = true
      toast(`Swooped to lane ${lanes[target.r].sum}!`);
    } else if (targets.length > 1) {
      // Let player choose swoop target
      mode = 'tailwindChooseSwoop';
      tailwindSwoopTargets = targets;
      showTailwindSwoopChoice();
      return;
    }
  }

  // Clear tailwind state and finish
  mode = null;
  tailwindPiece = null;
  tailwindOptions = null;
  tailwindSwoopTargets = null;
  finishTailwind();
}

function showTailwindSwoopChoice(){
  const oppIndex = 1-current;
  statusEl.textContent = `${players[oppIndex].name}: Choose swoop destination.`;

  // Highlight swoop targets
  for(const target of tailwindSwoopTargets){
    const el = locate(target.r, target.step);
    if(el){
      el.classList.add('highlight');
      el.onclick = () => handleTailwindSwoopChoice(target);
    }
  }
}

function handleTailwindSwoopChoice(target){
  if(mode !== 'tailwindChooseSwoop' || !tailwindPiece) return;

  const oppIndex = 1-current;
  const opp = players[oppIndex];

  // Find the actual piece in the opponent's pieces array
  const actualPiece = opp.pieces.find(p => p.r === tailwindPiece.r && p.step === tailwindPiece.step);

  if (actualPiece) {
    performMoveWithPush(actualPiece, target, true); // isSwoop = true
    toast(`Swooped to lane ${lanes[target.r].sum}!`);
  }

  // Clear tailwind state and finish
  mode = null;
  tailwindPiece = null;
  tailwindOptions = null;
  tailwindSwoopTargets = null;
  finishTailwind();
}

/* ---------- End turn logic ---------- */
function resolveDeterrents(pl){
  pl.pieces = pl.pieces.filter(pc=>{
    const L=lanes[pc.r].L; const sum=lanes[pc.r].sum;
    const dets = deterrents(L,sum);
    const onDet = dets.includes(pc.step);
    if(onDet){
      if(pc.carrying && lanes[pc.r].basket){
        const ce=cells[pc.r][centerCol];
        if(!ce.querySelector('.piece')){ const b=document.createElement('div'); b.className='piece'; b.textContent='🧺'; ce.appendChild(b); }
      }
      return false;
    }
    return true;
  });
}
function bank(){
  const pl = players[current];
  const kept=[];
  for(const pc of pl.pieces){
    const L=lanes[pc.r].L; const cps = checkpoints(L);
    tryPickupBasket(pc);
    if(pc.carrying){
      kept.push(pc);
    }else{
      let dest = null;
      for(const c of cps){ if(c<=pc.step) dest=c; }
      if(dest===null){ /* falls off */ }
      else { pc.step = dest; kept.push(pc); }
    }
  }
  pl.pieces = kept;
  resolveDeterrents(pl);
  let delivered=0;
  pl.pieces = pl.pieces.filter(pc=>{
    if(pc.carrying && pc.step===1){ delivered++; return false; }
    return true;
  });
  pl.score += delivered; if(delivered) toast(`${pl.name} delivered ${delivered}.`);
  pl.pieces.forEach(p=>p.active=false);

  // Earn a swoop token on Bank (not on Bust)
  pl.swoopTokens = (pl.swoopTokens||0) + 1;
  renderScores();

  // Check for victory after delivery
  if(checkVictory()) return;

  nextPlayer();
}
function bust(){
  const pl = players[current];
  const kept=[];
  for(const pc of pl.pieces){
    const onDet = (tileTypeAt(pc.r, pc.step) === 'Deterrent');
    const onCp = (tileTypeAt(pc.r, pc.step) === 'Checkpoint');
    if(onDet || onCp){ kept.push(pc); continue; }
    let dest = null;
    if(pc.carrying){ for(let s=pc.step; s<=lanes[pc.r].L; s++){ if(tileTypeAt(pc.r, s)==='Checkpoint'){ dest=s; break; } } }
    else { for(let s=pc.step; s>=1; s--){ if(tileTypeAt(pc.r, s)==='Checkpoint'){ dest=s; break; } } }
    if(dest===null){
      if(pc.carrying && lanes[pc.r].basket){
        const ce=cells[pc.r][centerCol];
        if(!ce.querySelector('.piece')){ const b=document.createElement('div'); b.className='piece'; b.textContent='🧺'; ce.appendChild(b); }
      }
    } else { pc.step = dest; kept.push(pc); }
  }
  pl.pieces = kept;
  resolveDeterrents(pl);
  pl.pieces.forEach(p=>p.active=false);

  // Check for victory after bust (in case any deliveries occurred)
  if(checkVictory()) return;

  nextPlayer();
}

/* ---------- Save / Load ---------- */
function getState(){
  return {
    version: 'v5.2',
    players: players.map(p=>({name:p.name, score:p.score, swoopTokens: p.swoopTokens||0, pieces:p.pieces.map(x=>({...x}))})),
    current,
    mode,
    rolled: rolled ? {d:[...rolled.d], pairs:[...rolled.pairs]} : null,
    selectedPair: selectedPair ? {...selectedPair} : null
  };
}
function setState(state){
  try{
    players[0].score = state.players[0].score;
    players[1].score = state.players[1].score;
    players[0].swoopTokens = (state.players[0].swoopTokens||0);
    players[1].swoopTokens = (state.players[1].swoopTokens||0);
    players[0].pieces = state.players[0].pieces || [];
    players[1].pieces = state.players[1].pieces || [];
    current = (state.current===0 || state.current===1) ? state.current : 0;
    mode = state.mode || 'preroll';
    rolled = state.rolled ? {d:[...state.rolled.d], pairs:[...state.rolled.pairs]} : null;
    selectedPair = state.selectedPair || null;
    renderDiceAndPairs();
    render();
    statusEl.textContent = `Loaded game. ${players[current].name}'s turn.`;
  }catch(e){
    console.error(e); toast('Invalid state.'); 
  }
}
function saveToFile(){
  const blob = new Blob([JSON.stringify(getState(), null, 2)], {type:'application/json'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href=url; a.download = 'swoop_state.json'; a.click();
  URL.revokeObjectURL(url);
}
function openLoadModal(){
  document.getElementById('loadTextarea').value='';
  document.getElementById('fileInput').value='';
  document.getElementById('loadModal').style.display='flex';
}
function closeLoadModal(){ document.getElementById('loadModal').style.display='none'; }
function loadFromText(txt){
  try{
    const state = JSON.parse(txt);
    setState(state);
    closeLoadModal();
  }catch(e){ console.error(e); toast('Could not parse JSON.'); }
}
document.getElementById('confirmLoad').onclick = ()=>{
  const txt = document.getElementById('loadTextarea').value.trim();
  if(txt) loadFromText(txt); else toast('Paste JSON or choose a file.');
};
document.getElementById('cancelLoad').onclick = closeLoadModal;
document.getElementById('fileInput').addEventListener('change', (ev)=>{
  const f = ev.target.files && ev.target.files[0]; if(!f) return;
  const rd = new FileReader();
  rd.onload = ()=> loadFromText(rd.result);
  rd.readAsText(f);
});
function quickSave(){ localStorage.setItem('SWOOP_STATE_V52', JSON.stringify(getState())); toast('Saved to browser.'); }
function quickLoad(){ const txt = localStorage.getItem('SWOOP_STATE_V52'); if(!txt){ toast('No quick save found.'); return; } loadFromText(txt); }

/* ---------- Wire up buttons ---------- */
rollBtn.onclick = ()=>{ if(mode!=='preroll') return; roll3(); };
useMoveBtn.onclick = ()=> usePairForMove();
useSwoopBtn.onclick = ()=> usePairForSwoop();
transferBtn.onclick = ()=> startTransfer();
cancelTransferBtn.onclick = ()=> cancelTransfer();
bankBtn.onclick = ()=>{
  if(mode==='preroll'){ bank(); }
  else if((mode==='rolled' || mode==='pairChosen') && !anyActionThisRoll()){ bust(); }
};
newBtn.onclick = startGame;
saveBtn.onclick = saveToFile;
loadBtn.onclick = openLoadModal;
qsaveBtn.onclick = quickSave;
qloadBtn.onclick = quickLoad;

/* ---------- Kickoff ---------- */
startGame();
</script>
</body>
</html>
